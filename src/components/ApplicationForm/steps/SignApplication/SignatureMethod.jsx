import { useEffect, useRef, useState } from "react";
import "@fontsource/caveat/500.css";
import SignatureCanvas from "react-signature-canvas";

function SigCanvas() {
  const [isEmpty, setIsEmpty] = useState(true);
  const signCanvasRef = useRef(null);

  useEffect(() => {
    const canvasRef = signCanvasRef.current;

    if (canvasRef) {
      canvasRef.clear();

      canvasRef._sigPad.onBegin = () => {
        setIsEmpty(false);
      };

      setIsEmpty(true);
    }

    return () => {
      // canvasRef?.off();
    };
  }, []);

  return (
    <div className="relative font-caveat text-5xl w-full h-full flex items-center justify-center text-gray-500">
      <SignatureCanvas
        ref={(ref) => {
          console.log("got ref", ref);
          signCanvasRef.current = ref;
        }}
        canvasProps={{ className: "relative", style: { width: "100%", height: "100%" } }}
      />
      {isEmpty && <span className="absolute z-10 left-1/2 top-22  transform -translate-x-1/2 ">Sign here</span>}
    </div>
  );
}

export default function SignatureMethod() {
  const [typedSignature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // Track signature method

  const toggleSignatureMethod = () => {
    if (signatureMethod === "type") {
      setSignature("");
    }
    setSignatureMethod(signatureMethod === "type" ? "draw" : "type");
  };

  return (
    <div className="space-y-4">
      <div className="w-full">
        <label className="block text-sm font-bold text-slate-800 mb-3">Signature: </label>

        {/* Signature Display Area */}
        <div className="w-full min-w-[350px] lg:w-1/2 h-42 relative border-2 border-slate-300 rounded-lg bg-white mb-4 flex items-center justify-center">
          <div id="signature-box" className="relative z-10  w-full h-full flex items-center justify-center ">
            {signatureMethod === "type" ? (
              <input
                id="signature"
                name="signature"
                type="text"
                placeholder="Type your name"
                autocomplete="none"
                aria-autocomplete="none"
                value={typedSignature}
                onChange={(e) => setSignature(e.target.value)}
                className="w-full h-full font-caveat text-5xl placeholder:text-gray-500 p-4 pt-20 border-0 bg-transparent text-center focus:outline-none focus:ring-0"
                required
              />
            ) : (
              <SigCanvas />
            )}
            {/** sign on line */}
            <div className="absolute w-2/3 h-0.5 bottom-8 border-t border-1/2 border-dashed border-gray-400 -z-10" />
          </div>
        </div>

        {/* Signature Method Buttons */}
        <div className="w-full lg:w-1/2 lg:min-w-[350px] flex gap-4">
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-3 px-6 rounded-lg border-2 font-medium transition-colors ${
              signatureMethod === "type"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            Type
          </button>
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-3 px-6 rounded-lg border-2 font-medium transition-colors ${
              signatureMethod === "draw"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            Draw
          </button>
        </div>
      </div>
      <div className="w-full max-w-xs">
        <label className="block text-sm font-bold text-slate-800 mb-2">Date of Signing: </label>
        <div className="w-full p-3 border border-slate-300 rounded-lg text-sm font-semibold text-slate-800 shadow-sm">
          {new Date().toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </div>
      </div>
    </div>
  );
}
